import React, { useState, useEffect } from "react";
import Modal from "@/Components/Modal";
import SecondaryButton from "@/Components/SecondaryButton";
import PrimaryButton from "@/Components/PrimaryButton";
import TextInput from "@/Components/TextInput";
import TextArea from "@/Components/TextArea";
import InputError from "@/Components/InputError";
import { useForm } from "@inertiajs/react";

const CreatePaymentModal = ({ isOpen, onClose, selectedItems, domains }) => {
    const { data, setData, post, processing, errors, reset } = useForm({
        ids: [],
        total_amount: '0.00',
        valid_until: '',
        note: ''
    });

    const getDefaultValidUntil = () => {
        const date = new Date();
        date.setDate(date.getDate() + 30);
        return date.toISOString().split('T')[0];
    };

    const getSuggestedAmount = () => {
        const defaultPricePerDomain = 50.00;
        return (selectedItems?.length || 0) * defaultPricePerDomain;
    };

    useEffect(() => {
        if (isOpen && selectedItems?.length > 0) {
            setData({
                ids: selectedItems,
                total_amount: getSuggestedAmount().toFixed(2),
                valid_until: getDefaultValidUntil(),
                note: `Payment order for ${selectedItems.length} domain${selectedItems.length > 1 ? 's' : ''}`
            });
        }
    }, [isOpen, selectedItems]);

    const handleSubmit = (e) => {
        e.preventDefault();
        
        post(route("domain-redemption.create-payment"), {
            onSuccess: () => {
                reset();
                onClose();
            },
            onError: (errors) => {
                console.error('Payment creation failed:', errors);
            }
        });
    };

    const handleClose = () => {
        reset();
        onClose();
    };

    const selectedDomains = domains?.filter(domain =>
        selectedItems?.includes(domain.delete_id)
    ) || [];

    if (!isOpen) {
        return null;
    }

    return (
        <Modal show={isOpen} onClose={handleClose} maxWidth="lg">
            <div className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    Create Payment Order
                </h2>

                <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-2">
                        Selected domains ({selectedItems.length}):
                    </p>
                    <div className="max-h-32 overflow-y-auto bg-gray-50 p-3 rounded border">
                        {selectedDomains.map((domain, index) => (
                            <div key={index} className="text-sm text-gray-700">
                                • {domain.name}
                            </div>
                        ))}
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Total Amount ($)
                        </label>
                        <TextInput
                            type="number"
                            step="0.01"
                            min="0.01"
                            value={data.total_amount}
                            handleChange={(e) => setData('total_amount', e.target.value)}
                            placeholder="Enter total amount"
                            className="w-full"
                            required
                        />
                        <InputError message={errors.total_amount} className="mt-1" />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Valid Until
                        </label>
                        <TextInput
                            type="date"
                            value={data.valid_until}
                            handleChange={(e) => setData('valid_until', e.target.value)}
                            min={new Date().toISOString().split('T')[0]}
                            className="w-full"
                            required
                        />
                        <p className="text-xs text-gray-500 mt-1">
                            Default: {getDefaultValidUntil()} (30 days from now)
                        </p>
                        <InputError message={errors.valid_until} className="mt-1" />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Note
                        </label>
                        <TextArea
                            value={data.note}
                            handleChange={(e) => setData('note', e.target.value)}
                            placeholder="Add any additional notes for this payment order..."
                            rows={3}
                            className="w-full"
                            maxLength={500}
                        />
                        <p className="text-xs text-gray-500 mt-1">
                            Optional: Add notes about this payment order (max 500 characters)
                        </p>
                        <InputError message={errors.note} className="mt-1" />
                    </div>

                    <div className="flex justify-end gap-3 pt-4">
                        <SecondaryButton 
                            type="button"
                            onClick={handleClose}
                            disabled={processing}
                        >
                            Cancel
                        </SecondaryButton>
                        <PrimaryButton 
                            type="submit"
                            disabled={processing}
                        >
                            {processing ? 'Creating...' : 'Create Payment Order'}
                        </PrimaryButton>
                    </div>
                </form>
            </div>
        </Modal>
    );
};

export default CreatePaymentModal;
