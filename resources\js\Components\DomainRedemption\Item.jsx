import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useRef, useState } from "react";
import { MdMoreVert } from "react-icons/md";
import "react-toastify/dist/ReactToastify.css";
import setDefaultDateFormat from "@/Util/setDefaultDateFormat";
import getRedemptionDays from "@/Util/getRedemptionDays";
import { router } from "@inertiajs/react";
import CreatePaymentModal from "@/Components/DomainRedemption/CreatePaymentModal";

export default function Item({ item, isSelected, onCheckboxChange }) {
    const [show, setShow] = useState(false);
    const [showCreatePaymentModal, setShowCreatePaymentModal] = useState(false);
    const ref = useRef();

    const createPayment = !item.redemption_order_id;

    const restoreApproved =
        item.paid_at && item.domain_deleted_at;

    const getPaymentStatus = () => {
        if (!item.redemption_order_id) {
            return "No Payment";
        } else if (item.paid_at && item.domain_deleted_at) {
            return "Payment Received";
        } else {
            return "Payment Pending";
        }
    };

    const getRedemptionDaysClass = (deletedAt) => {
        if (!deletedAt) return "";

        const deletedDate = new Date(deletedAt);
        const currentDate = new Date();
        const diffInMs = currentDate.getTime() - deletedDate.getTime();
        const daysInRedemption = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));

        if (daysInRedemption > 30) {
            return "text-red-600 font-semibold";
        } else if (daysInRedemption > 25) {
            return "text-orange-600 font-semibold";
        } else if (daysInRedemption > 20) {
            return "text-yellow-600";
        }

        return "text-green-600";
    };

    useOutsideClick(ref, () => setShow(false));

    const handleCheckboxChange = (e) => {
        if (!createPayment) return;
        onCheckboxChange(item.delete_id, getEventValue(e));
    };

    const handleRestoreApproved = () => {
        if (!restoreApproved) return;
        router.post(route("domain-redemption.approve"), {
            ids: [item.delete_id],
        });
    };

    const handleCreatePayment = () => {
        if (!createPayment) return;
        setShow(false); // Close dropdown
        setShowCreatePaymentModal(true);
    };

    const handleDelete = () => {
        router.post(route("domain-redemption.delete"), {
            ids: [item.delete_id],
        });
    };

    // console.log(item);

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    <Checkbox
                        name="select"
                        value="select"
                        checked={createPayment ? isSelected : false}
                        handleChange={handleCheckboxChange}
                        disabled={!createPayment}
                    />
                    <span>{item.name}</span>
                </label>
            </td>
            <td>
                <span>
                    {item.first_name} {item.last_name}
                </span>
            </td>
            <td>
                <span>{item.user_email}</span>
            </td>
            <td>
                <span>{getPaymentStatus()}</span>
            </td>
            <td>
                <span>{setDefaultDateFormat(item.domain_deleted_at)}</span>
            </td>
            <td>
                <span
                    className={getRedemptionDaysClass(item.domain_deleted_at)}
                >
                    {getRedemptionDays(item.domain_deleted_at)}
                </span>
            </td>
            <td>
                <span>{item.deleted_by}</span>
            </td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>

                    <DropDownContainer show={show}>
                        {createPayment && (
                            <button
                                className="hover:bg-gray-100 px-5 py-1 justify-start flex"
                                onClick={handleCreatePayment}
                            >
                                Create Payment
                            </button>
                        )}
                        {/* {restoreApproved && (
                            <button
                                className="hover:bg-gray-100 px-5 py-1 justify-start flex"
                                onClick={handleRestoreApproved}
                            >
                                Approved
                            </button>
                        )} */}
                        <button
                            className="hover:bg-gray-100 px-5 py-1 justify-start flex"
                            onClick={handleDelete}
                        >
                            Delete
                        </button>
                    </DropDownContainer>
                </span>
            </td>
            <CreatePaymentModal
                isOpen={showCreatePaymentModal}
                onClose={() => setShowCreatePaymentModal(false)}
                selectedItems={[item.delete_id]}
                domains={[item]}
            />
        </tr>
    );
}
