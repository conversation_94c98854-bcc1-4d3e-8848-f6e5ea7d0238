import React, { useState } from "react";
import { IoMdArrowBack } from "react-icons/io";
import { RiAttachment2 } from "react-icons/ri";
import EmailHeader from "@/Components/EmailHistory/Layout/Header";
import EmailFooter from "@/Components/EmailHistory/Layout/Footer";
import PaymentInvoice from "@/Components/EmailHistory/Mails/PaymentInvoice";
import ThirdExpiration from "@/Components/EmailHistory/Mails/ThirdExpiration";
import AuthenticationRequest from "@/Components/EmailHistory/Mails/AuthenticationRequest";
import TransferRefund from "@/Components/EmailHistory/Mails/TransferRefund";
import ExpirationNotice from "@/Components/EmailHistory/Mails/ExpirationNotice";
import IdentityVerificationVerified from "@/Components/EmailHistory/Mails/IdentityVerificationVerified";
import IdentityVerificationInProcess from "@/Components/EmailHistory/Mails/IdentityVerificationInProcess";
import OTPVerification from "@/Components/EmailHistory/Mails/OTPVerification";
import AddAccountCredit from "@/Components/EmailHistory/Mails/AddAccountCredit";
import BankTransferNotification from "@/Components/EmailHistory/Mails/BankTransferNotification";
import DomainRedemptionNotice from "@/Components/EmailHistory/Mails/DomainRedemptionNotice";
import ReportAbuse from "@/Components/EmailHistory/Mails/ReportAbuse";
import ClientsQuery from "@/Components/EmailHistory/Mails/ClientsQuery";
import DomainTransferRequestInitiated from "@/Components/EmailHistory/Mails/DomainTransferRequestInitiated";
import UserInvite from "@/Components/EmailHistory/Mails/UserInvite";

const getAttachmentUrl = (attachmentId) => {
    return `/system-email-history/download-attachment/${attachmentId}`;
};

export default function EmailDetails({ history, onBackClick, links }) {
    const [showAttachment, setShowAttachment] = useState(false);


    return (
        <div className="space-y-4">
            <div className="flex space-x-4 px-5">
                <button onClick={onBackClick} className="text-2xl pt-1">
                    <IoMdArrowBack />
                </button>
                <h1 className="text-3xl">{history.subject}</h1>
            </div>
            <div className="bg-white p-6">
                <p className="text-gray-700">
                    <strong>Recipient Name:</strong> {history.name}
                </p>
                <p className="text-gray-700">
                    <strong>Recipient Email:</strong> {history.recipient_email}
                </p>
                <p className="text-gray-700">
                    <strong>Date & Time Sent:</strong>{" "}
                    {new Intl.DateTimeFormat("en-US", {
                        month: "numeric",
                        day: "numeric",
                        year: "numeric",
                    }).format(new Date(history.created_at))}
                    &nbsp;&nbsp;
                    {new Intl.DateTimeFormat("en-US", {
                        hour: "numeric",
                        minute: "numeric",
                        second: "numeric",
                        hour12: true,
                    }).format(new Date(history.created_at))}
                </p>
                <p className="text-gray-700">
                    <strong>Email Type:</strong> {history.email_type}
                </p>
                {(() => {
                    try {
                        const parsedBody = JSON.parse(history.email_body);
                        return parsedBody?.ip && (
                            <p className="text-gray-700">
                                <strong>IP: </strong> {parsedBody.ip}
                            </p>
                        );
                    } catch (e) {
                        return null;
                    }
                })()}
                <div className="mt-4">
                    {history.attachment && (
                        <div className="mt-4">
                            <strong>Attachment: </strong>
                            <div className="pl-12">
                                <a
                                    href={getAttachmentUrl(history.id)}
                                    className="flex items-center space-x-1 text-blue-600 underline cursor-pointer border border-gray-300 rounded px-2 py-0.5 bg-gray-100 hover:bg-gray-200 max-w-[140px] text-sm"
                                    title="Download Attachment"
                                    download
                                >
                                    <RiAttachment2 className="text-base" />
                                    <span>domains_list.csv</span>
                                </a>
                            </div>
                            {showAttachment && (
                                <div className="mt-2 p-4 bg-gray-100 border rounded">
                                    <pre className="whitespace-pre-wrap">
                                        {history.attachment}
                                    </pre>
                                </div>
                            )}
                        </div>
                    )}
                    <strong>Email Message:</strong>
                    <div className="min-h-11 h-screen mt-2 p-4">
                        <EmailHeader />
                    
                        {history.component_type === "PaymentInvoice" ? (
                            <PaymentInvoice emailBody={history.email_body} links={links} />
                        ) : history.component_type === "ThirdExpiration" ? (
                            <ThirdExpiration emailData={history.email_body}  links={links} />
                        ) : history.component_type === "AuthenticationRequest" ? (
                            <AuthenticationRequest emailData={history.email_body}  links={links} />
                        ) : history.component_type === "TransferRefund" ? (
                            <TransferRefund emailData={history.email_body}  links={links} />
                        ) : history.component_type === "ExpirationNotice" ? (
                            <ExpirationNotice emailData={history.email_body}  links={links} />
                        ) : history.component_type === "IdentityVerificationVerified" ? (
                            <IdentityVerificationVerified emailBody={history.email_body}  links={links} />
                        ) : history.component_type === "IdentityVerificationInProcess" ? (
                            <IdentityVerificationInProcess emailBody={history.email_body}  links={links} />
                        ) : history.component_type === "OTPVerification" ? (
                            <OTPVerification emailBody={history.email_body} subject={history.subject}  links={links} />
                        ) : history.component_type === "AddAccountCredit" ? (
                            <AddAccountCredit emailData={history.email_body}  links={links} />
                        ) : history.component_type === "BankTransferNotification" ? (
                            <BankTransferNotification emailData={history.email_body} subject={history.subject}  links={links} />
                        ) : history.component_type === "DomainRedemptionNotice" ? (
                            <DomainRedemptionNotice emailBody={history.email_body} links={links} />
                        ) : history.component_type === "ReportAbuse" ? (
                            <ReportAbuse emailData={history.email_body} links={links} />
                        ) : history.component_type === "ClientsQuery" ?(
                            <ClientsQuery emailData={history.email_body} links={links} />
                        ) : history.component_type === "DomainTransferRequestInitiated" ? (
                            <DomainTransferRequestInitiated emailData={history.email_body} links={links} />
                        ) : history.component_type === "UserInvite" ? (
                            <UserInvite emailData={history.email_body} links={links} />
                        ) : (
                            <div>{history.email_body}</div>
                        )}
                        <EmailFooter />
                    </div>
                </div>
            </div>
        </div>
    );
}
